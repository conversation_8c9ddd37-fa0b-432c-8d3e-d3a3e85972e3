@use "./variables-auth.scss" as *;

/* Authentication Container Styles */
.auth-container {
  min-height: 100vh;
  min-height: 100dvh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--auth-bg-container);
  padding: 20px;
  box-sizing: border-box;
}

/* Authentication Card Styles */
.auth-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 48px 40px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 3px solid rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;
}

.auth-card:hover {
  box-shadow: var(--auth-shadow-hover);
}

/* Authentication Header Styles */
.auth-header {
  text-align: center;
  margin-bottom: 32px;
}

.auth-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--auth-text-primary);
  margin: 0 0 8px 0;
  letter-spacing: -0.5px;
}

.auth-subtitle {
  font-size: 14px;
  color: var(--auth-text-secondary);
  margin: 0;
  font-weight: 400;
}

/* Authentication Form Styles */
.auth-form {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* Custom Input Field Container */
.auth-input-container {
  position: relative;
  width: 100%;
  margin-bottom: 24px;
}

/* Input Field Wrapper - Fixed height container for input and icon */
.auth-input-wrapper {
  position: relative;
  width: 100%;
  height: 56px; /* Fixed height to prevent icon displacement */
  margin-bottom: 5px; /* Small gap before error messages */
}

/* Custom Input Field Styling */
.auth-input {
  // position: absolute;
  // top: 0;
  // left: 0;
  width: 100%;
  height: 100%;
  padding: 16px 16px 16px 48px;
  border: 2px solid #969393;
  border-radius: 8px;
  background-color: #ffffff;
  color: #2d3748;
  font-size: 16px;
  font-family: inherit;
  outline: none;
  transition:
    border-color 0.2s ease,
    box-shadow 0.2s ease;
  box-sizing: border-box;
}

.auth-input::placeholder {
  color: #718096;
  font-size: 16px;
}

.auth-input:hover {
  border-color: var(--auth-border-hover);
}

.auth-input:focus {
  border-color: var(--auth-border-focus);
  box-shadow: 0 0 0 3px rgba(26, 26, 26, 0.1);
}

.auth-input.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.auth-input:disabled {
  background-color: #f5f5f5;
  border-color: #e0e0e0;
  color: var(--auth-text-muted);
  cursor: not-allowed;
}

/* Input Icon Styling */
.auth-input-icon {
  position: absolute;
  left: 16px;
  top: 55%;
  transform: translateY(-52%);
  color: var(--auth-text-secondary);
  font-size: 20px;
  pointer-events: none;
  z-index: 2;
}

.auth-input-label {
  position: absolute;
  // left: 40px;
  // top: 140px;
  padding-bottom: 25px;
  transform: translateY(-50%);
  color: var(--auth-text-secondary);
  font-size: 14px;
  pointer-events: none;
  z-index: 2;
}
.auth-input-label:focus {
  content: none;
}

.auth-input:focus + .auth-input-icon,
.auth-input:not(:placeholder-shown) + .auth-input-icon {
  color: var(--auth-text-primary);
}

/* Password Toggle Icon */
.auth-password-toggle {
  position: absolute;
  right: 12px;
  top: 55%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--auth-text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  z-index: 2;
  transition:
    color 0.2s ease,
    background-color 0.2s ease;
}

.auth-password-toggle:hover {
  color: var(--auth-text-primary);
  background-color: rgba(0, 0, 0, 0.05);
}

.auth-password-toggle mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* Error Message Styling */
.auth-error-message {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #dc3545;
  font-size: 12px;
  margin-top: 0;
  min-height: 16px;
  position: relative;
  z-index: 1;
}

.auth-error-message mat-icon {
  font-size: 14px;
  width: 14px;
  height: 14px;
}

/* Custom Input Autofill Override */
.auth-input:-webkit-autofill,
.auth-input:-webkit-autofill:hover,
.auth-input:-webkit-autofill:focus,
.auth-input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px var(--auth-bg-card) inset !important;
  -webkit-text-fill-color: var(--auth-text-primary) !important;
  background-color: var(--auth-bg-card) !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

.auth-input:-moz-autofill {
  background-color: var(--auth-bg-card) !important;
  color: var(--auth-text-primary) !important;
}

/* OTP/Token Input Specific Styling */
.auth-otp-container {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  margin-bottom: 24px;
}

.auth-otp-input {
  width: 50px;
  height: 60px;
  border: 2px solid var(--auth-border-default);
  border-radius: 8px;
  background-color: var(--auth-bg-card);
  color: var(--auth-text-primary);
  font-size: 20px;
  font-weight: 600;
  text-align: center;
  outline: none;
  transition:
    border-color 0.2s ease,
    box-shadow 0.2s ease;
  box-sizing: border-box;
}

.auth-otp-input:hover {
  border-color: var(--auth-border-hover);
}

.auth-otp-input:focus {
  border-color: var(--auth-border-focus);
  box-shadow: 0 0 0 3px rgba(26, 26, 26, 0.1);
}

.auth-otp-input.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.auth-otp-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Authentication Button Styles */
.auth-button {
  background-color: #1a1a1a;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 16px;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.2s ease;
  margin-top: 8px;
  position: relative;
  min-height: 56px;
}

.auth-button:hover:not(:disabled) {
  background-color: #2a2a2a;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.auth-button:disabled {
  border: 1px solid rgb(225, 225, 225);
  background-color: #787878;
  color: #999;
  cursor: not-allowed;
}

/* Authentication Footer Styles */
.auth-footer {
  text-align: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.auth-footer p {
  color: var(--auth-text-secondary);
  font-size: 14px;
  margin: 0;
}

.auth-link {
  color: var(--auth-text-primary);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.auth-link:hover {
  color: #333;
  text-decoration: underline;
}

/* Loading Spinner Styles */
.spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.spinner ::ng-deep circle {
  stroke: var(--auth-button-text);
}

/* Material UI Button Overrides for Authentication */
.auth-button.mat-mdc-raised-button {
  background-color: var(--auth-button-bg) !important;
  color: var(--auth-button-text) !important;
}

.auth-button.mat-mdc-raised-button:hover:not(:disabled) {
  background-color: var(--auth-button-hover) !important;
}

.auth-button.mat-mdc-raised-button:disabled {
  background-color: var(--auth-button-disabled) !important;
  color: var(--auth-text-muted) !important;
}

/* Material UI Icon Button Overrides */
.auth-icon-button ::ng-deep .mat-mdc-button-touch-target {
  color: var(--auth-text-secondary) !important;
}

.auth-icon-button ::ng-deep mat-icon {
  color: var(--auth-text-secondary) !important;
}

/* Material UI Divider Overrides */
.auth-divider ::ng-deep .mat-mdc-divider {
  border-top-color: #f0f0f0 !important;
}

/* Full Width Utility Class */
.full-width {
  width: 100%;
  margin-bottom: 16px;
}

/* Button Content and Loading States */
.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
}

.button-spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.hidden {
  visibility: hidden;
  opacity: 0;
}

/* Back Button Styles */
.back-button {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--auth-text-secondary) !important;
  text-transform: none;
  font-size: 14px;
  margin: 0 auto;
  background: transparent !important;
}

.back-button:hover {
  color: var(--auth-text-primary) !important;
  background: transparent !important;
}

.back-to-login {
  text-align: center;
  padding-top: 24px;
}

/* Password Strength Indicator */
.password-strength {
  margin: 16px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.password-strength h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: var(--auth-text-primary);
  font-weight: 500;
}

.strength-requirement {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.requirement-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.requirement-icon.met {
  background-color: #28a745;
  color: white;
}

.requirement-icon.unmet {
  background-color: #dc3545;
  color: white;
}

.requirement-text {
  font-size: 13px;
}

.requirement-text.met {
  color: #28a745;
}

.requirement-text.unmet {
  color: #dc3545;
}

/* Resend Section Styles */
.resend-section {
  text-align: center;
  margin-bottom: 24px;
}

.resend-text {
  color: var(--auth-text-secondary);
  font-size: 14px;
  margin-bottom: 16px;
}

.resend-button {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  text-transform: none;
  margin: 0 auto;
  position: relative;
  background: transparent !important;
}

.resend-button:not(.disabled) {
  color: var(--auth-text-secondary) !important;
}

.resend-button:not(.disabled):hover {
  color: var(--auth-text-primary) !important;
  background: transparent !important;
}

.resend-button.disabled {
  color: var(--auth-text-muted) !important;
  cursor: not-allowed;
}

.resend-spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* Token Input Styles for Verify Reset Token Component */
.token-container {
  margin-bottom: 32px;
}

.token-inputs {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  margin-bottom: 16px;
}

.token-input {
  width: 50px;
  height: 60px;
  border: 2px solid var(--auth-border-default);
  border-radius: 8px;
  background-color: var(--auth-bg-card);
  color: var(--auth-text-primary);
  font-size: 20px;
  font-weight: 600;
  text-align: center;
  outline: none;
  transition: all 0.2s ease;
}

.token-input:focus {
  border-color: var(--auth-border-focus);
  box-shadow: 0 0 0 3px rgba(26, 26, 26, 0.1);
}

.token-input:hover {
  border-color: var(--auth-border-hover);
}

.token-input.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.token-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Token Error Message */
.token-error {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #dc3545;
  font-size: 14px;
  margin-top: 8px;
}

.token-error .error-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Responsive Design */
@media (max-width: 480px) {
  .auth-container {
    padding: 16px;
  }

  .auth-card {
    padding: 32px 24px;
  }

  .auth-title {
    font-size: 24px;
  }

  /* Responsive input adjustments */
  .auth-input-wrapper {
    height: 52px;
  }

  .auth-input {
    padding: 14px 14px 14px 44px;
    font-size: 16px;
  }

  .auth-input-icon {
    left: 14px;
    font-size: 18px;
  }

  .auth-password-toggle {
    right: 10px;
  }

  .auth-password-toggle mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }

  .auth-otp-input {
    width: 45px;
    height: 55px;
    font-size: 18px;
  }

  .auth-otp-container {
    gap: 6px;
  }
}

@media (max-width: 360px) {
  .auth-otp-input {
    width: 40px;
    height: 50px;
    font-size: 16px;
  }

  .auth-input {
    padding: 12px 12px 12px 40px;
  }

  .auth-input-icon {
    left: 12px;
    font-size: 16px;
  }
}
