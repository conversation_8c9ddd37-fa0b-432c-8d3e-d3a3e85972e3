<div class="card auth-card">
  <div class="text-center mb-4">
    <h2 class="fw-bold text-dark">Welcome Back</h2>
    <p class="text-muted">Sign in to continue</p>
  </div>

  <form class="auth-form" [formGroup]="loginForm" (ngSubmit)="onSubmit()">
    <!-- Email Field -->
    <div class="mb-5">
      <label for="email" class="auth-input-label text-dark fw-medium"
        >Email Address</label
      >
      <p-inputGroup class="auth-input-wrapper">
        <i class="pi pi-envelope auth-input-icon"></i>
        <input
          pInputText
          id="email"
          type="email"
          formControlName="email"
          class="auth-input py-2"
          placeholder="Enter your email"
          [class.error]="emailControl?.invalid && emailControl?.touched"
        />
      </p-inputGroup>
      <div
        *ngIf="emailControl?.hasError('required') && emailControl?.touched"
        class="auth-error-message ms-3"
      >
        <i class="pi pi-exclamation-triangle"></i>
        Email is required
      </div>
      <div
        *ngIf="emailControl?.hasError('email') && emailControl?.touched"
        class="auth-error-message"
      >
        <i class="pi pi-exclamation-triangle"></i>
        Please enter a valid email address
      </div>
    </div>

    <!-- Password Field -->
    <div class="mb-1">
      <label for="password" class="auth-input-label text-dark fw-medium"
        >Password</label
      >
      <p-inputGroup class="auth-input-wrapper">
        <i class="pi pi-lock auth-input-icon"></i>
        <input
          [type]="hidePassword ? 'password' : 'text'"
          pInputText
          id="password"
          formControlName="password"
          class="auth-input py-2"
          placeholder="Enter your password"
          [class.error]="passwordControl?.invalid && passwordControl?.touched"
        />
        <button
          type="button"
          (click)="togglePasswordVisibility()"
          class="auth-password-toggle"
          [attr.aria-label]="'Toggle password visibility'"
          [attr.aria-pressed]="!hidePassword"
        >
          <i class="pi pi-eye-slash" [class.pi-eye]="!hidePassword"></i>
        </button>
      </p-inputGroup>
      <div
        *ngIf="
          passwordControl?.hasError('required') && passwordControl?.touched
        "
        class="auth-error-message ms-3"
      >
        <i class="pi pi-exclamation-triangle"></i>
        Password is required
      </div>
    </div>

    <!-- Forgot Password -->
    <div class="mb-4 text-end">
      <button
        pButton
        pRipple
        type="button"
        label="Forgot Password?"
        class="p-button-text text-decoration-none text-primary p-0"
        (click)="onForgotPassword()"
      ></button>
    </div>

    <!-- Login Button -->
    <div class="d-grid mb-3">
      <button
        pButton
        pRipple
        type="submit"
        label="Login"
        class="p-button-primary py-2 fw-bold"
      ></button>
    </div>

    <!-- Divider -->
    <!-- <div class="position-relative text-center my-4">
      <hr class="border-1" />
      <span
        class="px-2 bg-white position-relative text-muted"
        style="top: -12px"
        >OR</span
      >
    </div>

     Social Login -->
    <!-- <div class="d-grid gap-2">
      <button
        pButton
        pRipple
        type="button"
        icon="pi pi-google"
        label="Continue with Google"
        class="p-button-outlined p-button-secondary"
      ></button>
    </div> -->
  </form>
</div>
