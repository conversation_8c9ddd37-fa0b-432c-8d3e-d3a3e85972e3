import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AuthenticationRoutingModule } from './authentication-routing.module';
import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { LoginComponent } from './Pages/login/login.component';
import { OtpVerificaitonComponent } from './Pages/otp-verificaiton/otp-verificaiton.component';
import { ReactiveFormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { PasswordModule } from 'primeng/password';
import { CheckboxModule } from 'primeng/checkbox';
import { FloatLabelModule } from 'primeng/floatlabel';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroup } from 'primeng/inputgroup';
import { ForgetPasswordComponent } from './Pages/forget-password/forget-password.component';
import { VerifyResetTokenComponent } from './Pages/verify-reset-token/verify-reset-token.component';
import { ResetPasswordComponent } from './Pages/reset-password/reset-password.component';

@NgModule({
  declarations: [LoginComponent, OtpVerificaitonComponent, ForgetPasswordComponent, VerifyResetTokenComponent, ResetPasswordComponent],
  imports: [
    CommonModule,
    AuthenticationRoutingModule,
    ToastModule,
    ReactiveFormsModule,
    ButtonModule,
    InputTextModule,
    PasswordModule,
    CheckboxModule,
    FloatLabelModule,
    InputGroupModule,
    InputGroup,
  ],
  providers: [MessageService],
})
export class AuthenticationModule {}
